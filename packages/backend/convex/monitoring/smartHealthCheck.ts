/**
 * 🚀 SMART HEALTH CHECK SYSTEM
 * 
 * Intelligent health monitoring with predictive failure detection
 * and bandwidth-optimized checking strategies
 */

import { query, action } from "../_generated/server";
import { v } from "convex/values";
import { logOptimization } from "../lib/bandwidthMonitor";

// Temporary catch-all context type for health-check helpers
type ConvexHttpContext = any;

/**
 * Smart health check with intelligent failure prediction
 * Replaces frequent health checks with intelligent monitoring
 */
export const smartHealthCheck = action({
  args: {
    enablePredictiveChecks: v.optional(v.boolean()),
    batchSystemChecks: v.optional(v.boolean()),
    alertOnlyOnFailures: v.optional(v.boolean()),
    includeBandwidthAnalysis: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    
    try {
      const healthStatus = {
        overall: "healthy" as "healthy" | "warning" | "critical",
        systems: {} as Record<string, any>,
        predictions: {} as Record<string, any>,
        bandwidthAnalysis: null as any,
        alerts: [] as string[],
      };
      
      // Core system health checks (batched for efficiency)
      if (args.batchSystemChecks !== false) {
        const systemChecks = await Promise.all([
          checkDatabaseHealth(ctx),
          checkCacheHealth(ctx),
          checkAPIHealth(ctx),
          checkProcessingQueues(ctx),
        ]);
        
        healthStatus.systems = {
          database: systemChecks[0],
          cache: systemChecks[1],
          api: systemChecks[2],
          queues: systemChecks[3],
        };
      }
      
      // Predictive health analysis (if enabled)
      if (args.enablePredictiveChecks) {
        healthStatus.predictions = await runPredictiveAnalysis(ctx);
      }
      
      // Bandwidth analysis (if enabled)
      if (args.includeBandwidthAnalysis) {
        healthStatus.bandwidthAnalysis = await analyzeBandwidthHealth(ctx);
      }
      
      // Determine overall health status
      const systemStatuses = Object.values(healthStatus.systems);
      const hasWarnings = systemStatuses.some((s: { status: string } | undefined) => s?.status === "warning");
      const hasCritical = systemStatuses.some((s: { status: string } | undefined) => s?.status === "critical");
      
      if (hasCritical) {
        healthStatus.overall = "critical";
        healthStatus.alerts.push("Critical system issues detected");
      } else if (hasWarnings) {
        healthStatus.overall = "warning";
        healthStatus.alerts.push("System warnings detected");
      }
      
      // Alert only on failures (if enabled)
      if (args.alertOnlyOnFailures && healthStatus.overall === "healthy") {
        return {
          success: true,
          status: "healthy",
          skipped: true,
          reason: "No alerts needed for healthy status",
          executionTime: Date.now() - startTime,
        };
      }
      
      const executionTime = Date.now() - startTime;
      
      // Log health check optimization
      await logOptimization(
        ctx,
        "smartHealthCheck",
        15000, // Estimated size of comprehensive health check
        JSON.stringify(healthStatus).length, // Actual response size
        executionTime,
        Object.keys(healthStatus.systems).length, // Systems checked
        1, // Single health result
        "smart_health_monitoring",
        false
      );
      
      return {
        success: true,
        ...healthStatus,
        executionTime,
        timestamp: Date.now(),
      };
      
    } catch (error) {
      console.error("Smart health check failed:", error);
      return {
        success: false,
        overall: "critical",
        error: error instanceof Error ? error.message : "Unknown error",
        executionTime: Date.now() - startTime,
      };
    }
  },
});

/**
 * Quick health check for emergency situations
 */
export const quickHealthCheck = query({
  args: {},
  handler: async (ctx) => {
    const startTime = Date.now();
    
    try {
      // Minimal health checks for emergency situations
      const quickStatus = {
        database: await quickDatabaseCheck(ctx),
        cache: await quickCacheCheck(ctx),
        timestamp: Date.now(),
        executionTime: 0,
      };
      
      quickStatus.executionTime = Date.now() - startTime;
      
      return {
        success: true,
        status: quickStatus.database && quickStatus.cache ? "healthy" : "warning",
        ...quickStatus,
      };
      
    } catch (error) {
      return {
        success: false,
        status: "critical",
        error: error instanceof Error ? error.message : "Unknown error",
        executionTime: Date.now() - startTime,
      };
    }
  },
});

/**
 * Check database health with performance metrics
 */
async function checkDatabaseHealth(ctx: any): Promise<{
  status: "healthy" | "warning" | "critical";
  responseTime: number;
  connections: number;
  queryPerformance: number;
  issues: string[];
}> {
  const startTime = Date.now();
  const issues: string[] = [];
  
  try {
    // Test basic database connectivity and performance
    const testQuery = await ctx.db.query("users").take(1);
    const responseTime = Date.now() - startTime;
    
    // Check query performance thresholds
    let queryPerformance = 100; // Percentage score
    if (responseTime > 1000) {
      queryPerformance = 50;
      issues.push("Slow database response time");
    } else if (responseTime > 500) {
      queryPerformance = 75;
      issues.push("Moderate database response time");
    }
    
    // Estimate connection health (simplified)
    const connections = Math.floor(Math.random() * 10) + 5; // Placeholder
    
    // Determine status
    let status: "healthy" | "warning" | "critical" = "healthy";
    if (responseTime > 2000 || queryPerformance < 50) {
      status = "critical";
    } else if (responseTime > 1000 || queryPerformance < 75) {
      status = "warning";
    }
    
    return {
      status,
      responseTime,
      connections,
      queryPerformance,
      issues,
    };
    
  } catch (error) {
    return {
      status: "critical",
      responseTime: Date.now() - startTime,
      connections: 0,
      queryPerformance: 0,
      issues: [`Database error: ${error instanceof Error ? error.message : "Unknown"}`],
    };
  }
}

/**
 * Check cache health and performance
 */
async function checkCacheHealth(ctx: ConvexHttpContext): Promise<{
  status: "healthy" | "warning" | "critical";
  hitRate: number;
  size: number;
  expiredEntries: number;
  issues: string[];
}> {
  const issues: string[] = [];
  
  try {
    // Get cache statistics
    const cacheEntries = await ctx.db.query("cache").take(1000);
    const now = Date.now();
    
    const expiredEntries = cacheEntries.filter((entry: any) => entry.expiresAt < now).length;
    const totalSize = cacheEntries.reduce((sum: number, entry: any) => sum + (entry.size || 0), 0);
    
    // Calculate approximate hit rate (simplified)
    const hitRate = Math.max(75 - expiredEntries * 2, 40); // Placeholder calculation
    
    // Determine status
    let status: "healthy" | "warning" | "critical" = "healthy";
    if (hitRate < 50 || expiredEntries > 100) {
      status = "critical";
      issues.push("Low cache hit rate or too many expired entries");
    } else if (hitRate < 70 || expiredEntries > 50) {
      status = "warning";
      issues.push("Suboptimal cache performance");
    }
    
    return {
      status,
      hitRate,
      size: totalSize,
      expiredEntries,
      issues,
    };
    
  } catch (error) {
    return {
      status: "critical",
      hitRate: 0,
      size: 0,
      expiredEntries: 0,
      issues: [`Cache error: ${error instanceof Error ? error.message : "Unknown"}`],
    };
  }
}

/**
 * Check API health and rate limiting status
 */
async function checkAPIHealth(ctx: any): Promise<{
  status: "healthy" | "warning" | "critical";
  dailyUsage: number;
  rateLimitStatus: string;
  errorRate: number;
  issues: string[];
}> {
  const issues: string[] = [];
  
  try {
    // Check recent API usage
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    const recentUsage = await ctx.db
      .query("twitterApiUsage")
      .withIndex("by_timestamp", (q: any) => q.gte("timestamp", oneDayAgo))
      .take(1000);
    
    const dailyUsage = recentUsage.length;
    const errorRequests = recentUsage.filter((u: any) => u.status === "error").length;
    const errorRate = dailyUsage > 0 ? (errorRequests / dailyUsage) * 100 : 0;
    
    // Determine rate limit status
    let rateLimitStatus = "normal";
    if (dailyUsage > 800) {
      rateLimitStatus = "approaching_limit";
      issues.push("Approaching daily API limit");
    } else if (dailyUsage > 900) {
      rateLimitStatus = "near_limit";
      issues.push("Near daily API limit");
    }
    
    // Determine status
    let status: "healthy" | "warning" | "critical" = "healthy";
    if (errorRate > 20 || dailyUsage > 950) {
      status = "critical";
    } else if (errorRate > 10 || dailyUsage > 800) {
      status = "warning";
    }
    
    return {
      status,
      dailyUsage,
      rateLimitStatus,
      errorRate,
      issues,
    };
    
  } catch (error) {
    return {
      status: "critical",
      dailyUsage: 0,
      rateLimitStatus: "unknown",
      errorRate: 100,
      issues: [`API error: ${error instanceof Error ? error.message : "Unknown"}`],
    };
  }
}

/**
 * Check processing queues and job status
 */
async function checkProcessingQueues(ctx: ConvexHttpContext): Promise<{
  status: "healthy" | "warning" | "critical";
  pendingJobs: number;
  failedJobs: number;
  averageProcessingTime: number;
  issues: string[];
}> {
  const issues: string[] = [];
  
  try {
    // Check job status
    const jobs = await ctx.db.query("jobs").take(500);
    const pendingJobs = jobs.filter((j: any) => j.status === "pending").length;
    const failedJobs = jobs.filter((j: any) => j.status === "failed").length;
    const runningJobs = jobs.filter((j: any) => j.status === "running");
    
    // Calculate average processing time for running jobs
    const now = Date.now();
    const averageProcessingTime = runningJobs.length > 0
      ? runningJobs.reduce((sum: number, job: any) => sum + (now - job.createdAt), 0) / runningJobs.length
      : 0;
    
    // Determine status
    let status: "healthy" | "warning" | "critical" = "healthy";
    if (pendingJobs > 100 || failedJobs > 50 || averageProcessingTime > 300000) {
      status = "critical";
      issues.push("High queue backlog or processing delays");
    } else if (pendingJobs > 50 || failedJobs > 20 || averageProcessingTime > 120000) {
      status = "warning";
      issues.push("Moderate queue backlog");
    }
    
    return {
      status,
      pendingJobs,
      failedJobs,
      averageProcessingTime,
      issues,
    };
    
  } catch (error) {
    return {
      status: "critical",
      pendingJobs: 0,
      failedJobs: 0,
      averageProcessingTime: 0,
      issues: [`Queue error: ${error instanceof Error ? error.message : "Unknown"}`],
    };
  }
}

/**
 * Run predictive health analysis
 */
async function runPredictiveAnalysis(ctx: ConvexHttpContext): Promise<{
  bandwidthTrend: "increasing" | "stable" | "decreasing";
  capacityRisk: "low" | "medium" | "high";
  costProjection: number;
  recommendations: string[];
}> {
  try {
    // Analyze bandwidth trends over the last week
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const recentLogs = await ctx.db
      .query("bandwidthLogs")
      .withIndex("by_timestamp", (q: any) => q.gte("timestamp", sevenDaysAgo))
      .take(1000);
    
    // Simple trend analysis
    const dailyUsage: Record<number, number> = {};
    recentLogs.forEach((log: any) => {
      const day = Math.floor(log.timestamp / (24 * 60 * 60 * 1000));
      if (!dailyUsage[day]) dailyUsage[day] = 0;
      dailyUsage[day] += log.bytesRead;
    });
    
    const days = Object.keys(dailyUsage).sort();
    const recentDays = days.slice(-3);
    const earlierDays = days.slice(0, 3);
    
    const recentAvg = recentDays.reduce((sum: number, day: string) => sum + dailyUsage[parseInt(day)], 0) / recentDays.length;
    const earlierAvg = earlierDays.reduce((sum: number, day: string) => sum + dailyUsage[parseInt(day)], 0) / earlierDays.length;
    
    let bandwidthTrend: "increasing" | "stable" | "decreasing";
    if (recentAvg > earlierAvg * 1.2) {
      bandwidthTrend = "increasing";
    } else if (recentAvg < earlierAvg * 0.8) {
      bandwidthTrend = "decreasing";
    } else {
      bandwidthTrend = "stable";
    }
    
    // Capacity risk assessment
    const totalDailyBandwidth = recentAvg;
    let capacityRisk: "low" | "medium" | "high";
    if (totalDailyBandwidth > 1000000000) { // > 1GB per day
      capacityRisk = "high";
    } else if (totalDailyBandwidth > 500000000) { // > 500MB per day
      capacityRisk = "medium";
    } else {
      capacityRisk = "low";
    }
    
    // Cost projection (simplified)
    const monthlyCostProjection = (totalDailyBandwidth * 30) / 1000000000 * 0.10; // $0.10 per GB
    
    // Generate recommendations
    const recommendations: string[] = [];
    if (bandwidthTrend === "increasing") {
      recommendations.push("Consider implementing additional caching strategies");
    }
    if (capacityRisk === "high") {
      recommendations.push("Review and optimize high-bandwidth operations");
    }
    if (monthlyCostProjection > 50) {
      recommendations.push("Cost optimization recommended for next month");
    }
    
    return {
      bandwidthTrend,
      capacityRisk,
      costProjection: monthlyCostProjection,
      recommendations,
    };
    
  } catch (error) {
    return {
      bandwidthTrend: "stable",
      capacityRisk: "low",
      costProjection: 0,
      recommendations: ["Unable to generate predictions due to data access issues"],
    };
  }
}

/**
 * Analyze bandwidth health and optimization opportunities
 */
async function analyzeBandwidthHealth(ctx: ConvexHttpContext): Promise<{
  currentUsage: number;
  optimizationSavings: number;
  cacheEfficiency: number;
  recommendations: string[];
}> {
  try {
    // Get recent bandwidth usage
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    const recentLogs = await ctx.db
      .query("bandwidthLogs")
      .withIndex("by_timestamp", (q: any) => q.gte("timestamp", oneDayAgo))
      .take(1000);
    
    const currentUsage = recentLogs.reduce((sum: number, log: any) => sum + log.bytesRead, 0);
    const totalSavings = recentLogs.reduce((sum: number, log: any) => sum + (log.estimatedSavings || 0), 0);
    const cacheHits = recentLogs.filter((log: any) => log.cacheHit).length;
    const cacheEfficiency = recentLogs.length > 0 ? (cacheHits / recentLogs.length) * 100 : 0;
    
    const recommendations: string[] = [];
    if (cacheEfficiency < 70) {
      recommendations.push("Improve cache hit rate through better TTL configuration");
    }
    if (totalSavings < currentUsage * 0.5) {
      recommendations.push("Additional optimization opportunities available");
    }
    
    return {
      currentUsage,
      optimizationSavings: totalSavings,
      cacheEfficiency,
      recommendations,
    };
    
  } catch (error) {
    return {
      currentUsage: 0,
      optimizationSavings: 0,
      cacheEfficiency: 0,
      recommendations: ["Unable to analyze bandwidth health"],
    };
  }
}

/**
 * Quick database connectivity check
 */
async function quickDatabaseCheck(ctx: ConvexHttpContext): Promise<boolean> {
  try {
    await ctx.db.query("users").take(1);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Quick cache availability check
 */
async function quickCacheCheck(ctx: ConvexHttpContext): Promise<boolean> {
  try {
    await ctx.db.query("cache").take(1);
    return true;
  } catch (error) {
    return false;
  }
}